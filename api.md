# Yggdrasil Text RPG API Documentation

This document outlines all API endpoints for the Yggdrasil text RPG game, organized by functionality.

## World Management

### GET `/worlds`
**Purpose**: List all available worlds
**Response**:
```json
{
  "worlds": [
    {
      "id": "world-1",
      "name": "The World of Eldoria",
      "description": "A world filled with magic and adventure"
    }
  ]
}
```

### GET `/worlds/{worldId}`
**Purpose**: Get detailed world information
**Response**:
```json
{
  "worldId": "world-1",
  "worldName": "The World of Eldoria",
  "worldDescription": "A world filled with magic and adventure",
  "genre": "Fantasy",
  "tone": "Epic adventure",
  "central_theme": "Exploration and discovery",
  "world_state": {
    "time": "10:00 AM",
    "weather": "Sunny",
    "weather_description": "The sun is shining brightly"
  }
}
```

### POST `/worlds/generate`
**Purpose**: Generate a new world using the WorldAgent
**Request**:
```json
{
  "genre": "Fantasy",
  "theme": "A world where magic is fading",
  "tone": "Melancholic adventure"
}
```
**Response**: Complete world object

## Player Management

### POST `/players/create`
**Purpose**: Create a new player
**Request**:
```json
{
  "username": "PlayerOne",
  "starting_world": "world-1"
}
```
**Response**: New player object

### GET `/players/{playerId}`
**Purpose**: Get player information
**Response**:
```json
{
  "playerId": "player-1",
  "username": "PlayerOne",
  "unlock_lvl": 3,
  "location": "The Forest of Eldoria",
  "inventory": {
    "items": [
      {
        "name": "Iron Sword",
        "description": "A simple but effective weapon",
        "quantity": 1,
        "price": 50
      }
    ],
    "currency": 100
  },
  "relationships": [
    {
      "target": "npc-1",
      "attitude": 75,
      "trust": 60,
      "familiarity": 40
    }
  ]
}
```

### PUT `/players/{playerId}/move`
**Purpose**: Move player to a new location
**Request**:
```json
{
  "destination": "The Cave of Eldoria"
}
```
**Response**:
```json
{
  "success": true,
  "location": {
    "name": "The Cave of Eldoria",
    "description": "A dark, mysterious cave with glowing crystals",
    "biome": "cavern",
    "neighbors": ["The Forest of Eldoria", "The Mountain Path"]
  },
  "arrival_event": {
    "description": "As you enter the cave, you feel a cold breeze and hear distant echoes.",
    "encounters": ["A small bat colony hangs from the ceiling"]
  }
}
```

## Location & Exploration

### GET `/worlds/{worldId}/locations`
**Purpose**: Get all locations in a world
**Query Params**: `unlock_lvl` (optional, to filter by player level)
**Response**:
```json
{
  "locations": [
    {
      "id": "loc-1",
      "name": "The Forest of Eldoria",
      "description": "A dense forest with ancient trees",
      "biome": "forest",
      "unlock_lvl": 1,
      "neighbors": ["The Cave of Eldoria", "The Village of Eldoria"]
    }
  ]
}
```

### GET `/worlds/{worldId}/locations/{locationId}`
**Purpose**: Get detailed location information
**Response**:
```json
{
  "id": "loc-1",
  "name": "The Forest of Eldoria",
  "description": "A dense forest with ancient trees",
  "biome": "forest",
  "unlock_lvl": 1,
  "detailed_description": "Towering trees with emerald leaves filter the sunlight...",
  "points_of_interest": [
    "An ancient stone circle lies in a small clearing",
    "A babbling brook winds through the undergrowth"
  ],
  "available_interactions": [
    "Explore the stone circle",
    "Follow the brook upstream"
  ],
  "npcs_present": ["forest_guardian", "wandering_merchant"]
}
```

### POST `/worlds/{worldId}/locations/{locationId}/interact`
**Purpose**: Interact with a location feature
**Request**:
```json
{
  "playerId": "player-1",
  "interaction": "Explore the stone circle"
}
```
**Response**:
```json
{
  "result": "You approach the ancient stone circle. The stones are covered in mysterious runes that seem to glow faintly.",
  "discoveries": [
    {
      "type": "item",
      "item": {
        "name": "Runic Fragment",
        "description": "A piece of stone with glowing runes",
        "quantity": 1
      }
    }
  ],
  "new_interactions": ["Examine the runes", "Place the fragment on the altar"]
}
```

## NPC Interaction

### GET `/worlds/{worldId}/npcs`
**Purpose**: Get all NPCs in a world
**Query Params**: `location` (optional, to filter by location)
**Response**:
```json
{
  "npcs": [
    {
      "id": "npc-1",
      "name": "Elara the Forest Guardian",
      "location": "The Forest of Eldoria",
      "role": "Guardian",
      "race": "Elf"
    }
  ]
}
```

### POST `/npcs/{npcId}/talk`
**Purpose**: Talk to an NPC
**Request**:
```json
{
  "player_id": "player-1",
  "message": "Do you know anything about the ancient stone circle?"
}
```
**Response**:
```json
{
  "npc_id": "npc-1",
  "name": "Elara the Forest Guardian",
  "response": "The stone circle? It's a place of old magic, from before my time even. They say it was built to communicate with the spirits of the forest. Many seek its power, but few understand its true purpose.",
  "relationship_change": {
    "attitude": 2,
    "familiarity": 5
  },
  "knowledge_gained": ["The stone circle is ancient", "It's connected to forest spirits"]
}
```

### POST `/npcs/{npcId}/quest`
**Purpose**: Get or complete quests from NPCs
**Request**:
```json
{
  "player_id": "player-1",
  "action": "request_quest" // or "complete_quest"
}
```
**Response**:
```json
{
  "quest": {
    "id": "quest-1",
    "title": "The Lost Amulet",
    "description": "Elara has asked you to find her lost amulet, which was stolen by forest sprites.",
    "objectives": ["Find the sprite hideout", "Recover the amulet"],
    "rewards": {
      "currency": 50,
      "items": [{"name": "Potion of Healing", "quantity": 2}],
      "relationship": {"npc_id": "npc-1", "attitude": 10, "trust": 15}
    }
  }
}
```

## Inventory & Items

### GET `/inventory/{playerId}`
**Purpose**: Get player's inventory
**Response**:
```json
{
  "items": [
    {
      "id": "item-1",
      "name": "Iron Sword",
      "description": "A simple but effective weapon",
      "quantity": 1,
      "price": 50
    }
  ],
  "currency": 100,
  "capacity": {"max": 20, "used": 1}
}
```

### POST `/inventory/use`
**Purpose**: Use an item from the player's inventory
**Request**:
```json
{
  "playerId": "player-1",
  "itemId": "item-2",
  "target": "self" // or could be an NPC or object ID
}
```
**Response**:
```json
{
  "result": "You drink the healing potion and feel revitalized.",
  "effects": ["Restored 20 health points"],
  "item_consumed": true,
  "remaining_quantity": 1
}
```

## Trading & Merchants

### GET `/merchants/{worldId}`
**Purpose**: Get all merchants in a world
**Query Params**: `location` (optional, to filter by location)
**Response**:
```json
{
  "merchants": [
    {
      "id": "merchant-1",
      "name": "Doran the Wandering Merchant",
      "location": "The Forest of Eldoria",
      "specialization": "Potions and Herbs"
    }
  ]
}
```

### GET `/merchants/{merchantId}/inventory`
**Purpose**: View merchant's goods
**Response**:
```json
{
  "merchant_id": "merchant-1",
  "name": "Doran the Wandering Merchant",
  "inventory": [
    {
      "id": "item-2",
      "name": "Healing Potion",
      "description": "Restores 20 health points",
      "price": 25,
      "quantity_available": 5
    }
  ],
  "buying_rate": 0.5, // merchant buys at 50% of selling price
  "special_deals": [
    {
      "buy_item": "Runic Fragment",
      "offer": "Will pay double the normal price"
    }
  ]
}
```

### POST `/merchants/{merchantId}/trade`
**Purpose**: Buy or sell items
**Request**:
```json
{
  "playerId": "player-1",
  "merchantId": "merchant-1",
  "action": "buy", // or "sell"
  "itemId": "item-2",
  "quantity": 2
}
```
**Response**:
```json
{
  "success": true,
  "transaction": {
    "itemName": "Healing Potion",
    "quantity": 2,
    "total_price": 50,
    "playerCurrencyRemaining": 50
  },
  "merchant_dialogue": "Ah, healing potions! A wise investment. These are freshly brewed from herbs I gathered in the southern forest."
}
```

## Game State & Event Log

### GET `/logs/{playerId}`
**Purpose**: Get player's game history/log
**Query Params**: `limit` (max entries), `offset` (pagination)
**Response**:
```json
{
  "logs": [
    {
      "timestamp": "2025-05-02T18:30:22Z",
      "type": "movement",
      "description": "Traveled to The Forest of Eldoria"
    },
    {
      "timestamp": "2025-05-02T18:35:10Z",
      "type": "discovery",
      "description": "Found the ancient stone circle"
    },
    {
      "timestamp": "2025-05-02T18:40:05Z",
      "type": "dialogue",
      "npc": "Elara the Forest Guardian",
      "summary": "Learned about the history of the stone circle"
    }
  ]
}
```

### GET `/worlds/{worldId}/state`
**Purpose**: Get current world state (time, weather, events)
**Response**:
```json
{
  "time": "10:30 AM",
  "day_cycle": "Morning",
  "weather": "Light Rain",
  "weather_description": "A gentle rain falls, creating a soothing rhythm on the leaves.",
  "active_world_events": [
    {
      "id": "event-1",
      "name": "The Sprite Mischief",
      "description": "Forest sprites have been stealing trinkets from travelers.",
      "affected_locations": ["The Forest of Eldoria", "The Village Outskirts"]
    }
  ]
}
```
