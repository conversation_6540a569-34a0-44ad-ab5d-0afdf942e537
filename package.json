{"name": "yggdrasil.demo", "module": "src/index.ts", "type": "module", "scripts": {"mastra:dev": "<PERSON>ra dev", "dev": "bun run src/index.ts", "build": "bun build examples/basic-flow.ts --outdir ./dist --target node"}, "devDependencies": {"@biomejs/biome": "^2.2.0", "@types/bun": "^1.2.20", "type-fest": "^4.41.0"}, "peerDependencies": {"typescript": "^5.9.2"}, "dependencies": {"@ai-sdk/anthropic": "^2.0.4", "@ai-sdk/google": "^2.0.6", "@ai-sdk/groq": "^2.0.9", "@ai-sdk/openai": "^2.0.14", "@ai-sdk/xai": "^2.0.7", "@mastra/core": "0.0.0-ai-v5-20250625173645", "@mastra/libsql": "0.0.0-ai-v5-20250625173645", "@mastra/loggers": "0.0.0-ai-v5-20250625173645", "@mastra/memory": "0.0.0-ai-v5-20250625173645", "@mastra/pg": "0.0.0-ai-v5-20250625173645", "@mastra/rag": "0.0.0-ai-v5-20250625173645", "@mastra/server": "0.0.0-ai-v5-20250625173645", "@openrouter/ai-sdk-provider": "^1.1.2", "ai": "^5.0.14", "bunx": "^0.1.0", "dotenv": "^17.2.1", "hono": "^4.9.1", "mathjs": "^14.6.0", "nanoid": "^5.1.5", "ollama-ai-provider": "^1.2.0", "surrealdb": "^1.3.2", "tsc": "^2.0.4", "zod": "^4.0.17"}}