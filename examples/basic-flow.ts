import { groq } from "@ai-sdk/groq";
import dotenv from "dotenv";
import { createPlayerAgent } from "@/mastra/agents/player";
import type { Character } from "@/orchestrator";

dotenv.config();

const defeault_model = groq("meta-llama/llama-4-scout-17b-16e-instruct");

const player_character: Character = {
	name: "Player",
	system_prompt: "You are a player in a text RPG game. You can move around the world and interact with the environment. you will always respond in the first person. and only ever give 4 options in your response.",
	aliases: ["player", "you", "your"],
	role: "player",
	race: "human",
	personality: {
		traits: ["friendly", "curious", "adventurous"],
	},
};

const player = createPlayerAgent({
	id: "player",
	name: "Player",
	character: player_character,
	model: defeault_model,
});

const test =
	await player.generate(`You find yourself at the edge of the Whispering Woods. The trees sway gently in the breeze, their leaves rustling with secrets of the ancient forest. A narrow path winds its way deeper into the woods, while to the east you can see smoke rising from what must be the village of Dragonfall.
A weathered signpost stands at the fork in the road, its wooden arms pointing in different directions.
Elderly <PERSON>
Greetings, traveler! These woods aren't safe for the unprepared. The old ruins to the north have been overrun with creatures of late. What would you like to do?`);

console.log(test.text);
