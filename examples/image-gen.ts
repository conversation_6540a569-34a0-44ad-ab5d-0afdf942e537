import { google } from "@ai-sdk/google";
import { groq } from "@ai-sdk/groq";
import { openai } from "@ai-sdk/openai";
import { RuntimeContext } from "@mastra/core/runtime-context";
import { experimental_generateImage, generateText } from "ai";
import { createWorldAgent, type WorldAgentRuntimeContext } from "@/mastra/agents/world";

const world_agent = createWorldAgent(groq("meta-llama/llama-4-scout-17b-16e-instruct"));

const rc = new RuntimeContext<WorldAgentRuntimeContext>();
rc.set("type", "create-world");
const world = await world_agent.generate("Generate me a scifi world", { runtimeContext: rc as RuntimeContext<unknown> });

console.log(world.text);

const { image } = await experimental_generateImage({
	model: openai.image("gpt-image-1"),
	prompt: `given the following world description, please generate a world map for our text rpg, the map must be detailed and include all major features and key locations. ${world.text}`,
	n: 1,
	size: "1536x1024",
});

// const result = await generateText({
// 	model: google("gemini-2.5-flash"),
// 	providerOptions: {
// 		google: { responseModalities: ["TEXT", "IMAGE"] },
// 	},
// 	prompt: `given the following world description, please generate a world map for our text rpg, the map must be detailed and include all major features and key locations. ${world.text}`,
// });

// for (const file of result.files) {
// 	if (file.mimeType.startsWith("image/")) {
// 		await Bun.write("world-google.png", file.uint8Array);
// 	}
// }

// save image to file
await Bun.write("world_3.png", image.uint8Array);
