import path from "node:path";
import { groq } from "@ai-sdk/groq";
import { openai } from "@ai-sdk/openai";
import { createNpcAgent, type NpcAgent } from "../src/mastra/agents/npc";
import { createWorldAgent } from "../src/mastra/agents/world";
import { getAgentOptions, getCharacterFromFile } from "../src/utils/npcs";

const playerId = "soya-miruku";
const worldId = "world-1-steampunk";

const npcPath = path.join(process.cwd(), "assets/characters/seraphine_virellia/character.json");
const model = groq("llama3-70b-8192");
// const model = openai("gpt-4.1-mini");

const character = await getCharacterFromFile(npcPath);

const npc_agent = createNpcAgent({
	character,
	model,
	playerId,
	worldId,
});

const world_agent = createWorldAgent(model);

// Terminal color codes
const colors = {
	reset: "\x1b[0m",
	bold: "\x1b[1m",
	cyan: "\x1b[36m",
	yellow: "\x1b[33m",
	green: "\x1b[32m",
	magenta: "\x1b[35m",
	blue: "\x1b[94m",
	gray: "\x1b[90m",
};

// Interactive console session function
async function startInteractiveSession(npc: NpcAgent) {
	console.log(`\n${colors.magenta}${"=".repeat(60)}${colors.reset}`);
	console.log(`${colors.bold}${colors.green}Interactive session started with ${character.name}${colors.reset}`);
	console.log(`${colors.gray}Type your messages and press Enter. Type 'exit' or 'quit' to end the session.${colors.reset}`);
	console.log(`${colors.magenta}${"=".repeat(60)}${colors.reset}\n`);

	// Set up stdin to read line by line
	process.stdin.setEncoding("utf-8");

	// Process user input
	const askQuestion = () => {
		process.stdout.write(`\n${colors.yellow}${playerId}:${colors.reset} `);
	};

	// Start the conversation
	askQuestion();

	for await (const line of process.stdin) {
		const input = line.trim();

		// Check if user wants to exit
		if (input.toLowerCase() === "exit" || input.toLowerCase() === "quit") {
			console.log(`\n${colors.magenta}${"=".repeat(60)}${colors.reset}`);
			console.log(`${colors.bold}${colors.green}Ending session. Goodbye!${colors.reset}`);
			console.log(`${colors.magenta}${"=".repeat(60)}${colors.reset}`);
			process.exit(0);
		}

		// Process the input through the NPC agent
		const response = await npc.stream(input, getAgentOptions(playerId, worldId, character));

		// Display the response with proper formatting
		process.stdout.write(`\n${colors.bold}${colors.cyan}${character.name}:${colors.reset} `);

		let responseText = "";
		for await (const chunk of response.textStream) {
			responseText += chunk;
			process.stdout.write(`${chunk}`);
		}

		console.log();
		// Ask for next input
		askQuestion();
	}
}

// Start the interactive session
await startInteractiveSession(npc_agent);
