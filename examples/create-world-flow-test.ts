import { create_mastra_instance } from "@/mastra";

const mastra = create_mastra_instance();

// const result = await mastra.getWorkflow("create_world_workflow").execute({
// 	inputData: {
// 		prompt: "Please generate me a high fantasy world with a rich lore and a lot of details for my text rpg game",
// 	},
// });

const workflow = mastra.getWorkflow("create_world_workflow");
const run = await workflow.createRunAsync();

const result = await run.start({ inputData: { prompt: "Please generate me a high fantasy world with a rich lore and a lot of details for my text rpg game" } });

console.log(result);
