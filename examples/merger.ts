import fs from "node:fs/promises";
import path from "node:path";
import { mergeMarkdownFilesFromDirectory, mergeMarkdowns } from "../src/utils/helpers";

const directories = await fs.readdir(path.join(process.cwd(), "examples/output"));

const novel = directories[0];

if (!novel) {
  throw new Error("No novel found");
}

const sourceDir = path.join(process.cwd(), "examples/output", novel);
const files = await fs.readdir(sourceDir);

const markdownFiles = files.filter((file) => file.endsWith(".md"));

const filteredFiles = markdownFiles.filter((file) => file.includes("chapter"));
const contents = await Promise.all(filteredFiles.map((file) => fs.readFile(path.join(sourceDir, file), "utf-8")));
const content = await mergeMarkdowns(contents);

console.log(content);
