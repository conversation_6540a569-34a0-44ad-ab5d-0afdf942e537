import { Agent, type CoreMessage, type CoreUserMessage } from "@mastra/core";
import { ollama } from "ollama-ai-provider";
import { google } from "@ai-sdk/google";
import path from "node:path";
import fs from "node:fs";
import { generateText } from "ai";
import { createOpenRouter, openrouter } from "@openrouter/ai-sdk-provider";
import { openai } from "@ai-sdk/openai";

const openRouter = createOpenRouter({
	apiKey: process.env.OPENROUTER_API_KEY,
});

const model = openrouter("google/gemini-2.5-flash-preview-05-20");
const model_local = ollama("gemma3:27b");
const model_openai = openai("gpt-4o");

const system_prompt = `You are a world map interpreter. You are given a world map image and you need to generate the following details:

    Your task is to analyze a given world map image and extract structured information about accessible locations and their connections. 
    1.
      - Identify all locations where a player can visit or traverse either by foot or boat or air. Exclude any hidden or special locations from your output.
      - For each accessible location, determine its neighboring locations. If two locations are neighbors, ensure that this relationship is bidirectional (i.e., both locations list each other as neighbors).
      - Present your results as a JSON object in the following format:
      {
        [location_name]: [neighbor_1, neighbor_2, neighbor_3, ...],
        [location_name_2]: [neighbor_1, neighbor_2, neighbor_3, ...],
        // etc.
      }

    2.
      - Look at the world map image and identify between 3 to 6 unique biomes and put each of the locations into one of the biomes and give descriptions for each locations.
      - Present your results as a JSON object in the following format:
      {
        [Biome_name]: {
          [location_name]: [description],
          [location_name_2]: [description],
          // etc.
        }
      }


    overall output should be a JSON object in the following format:
    {
      connections: {
        [location_name]: [description],
        [location_name_2]: [description],
        // etc.
      },
      biomes: {
        [Biome_name]: {
          [location_name]: [description],
          [location_name_2]: [description],
          // etc.
        }
      }
}`;

const image_agent = new Agent({
	name: "image-agent",
	instructions: system_prompt,
	model,
});

console.log(import.meta.dirname);

const image_arr = await Bun.file(path.join(import.meta.dirname, "../assets/maps/1/_world.png")).arrayBuffer();
const image_buff = Buffer.from(image_arr);
const image_uint8 = new Uint8Array(image_buff);

const response = await image_agent.generate([
	{
		role: "user",
		content: [{ type: "image", image: image_uint8 }],
	},
	{
		role: "user",
		content: "Please analyze the image and generate the following details: ",
	},
]);

console.log(response.text);

// const re = await generateText({
// 	model: model,
// 	system: system_prompt,
// 	temperature: 0,
// 	messages: [
// 		{
// 			role: "user",
// 			content: [{ type: "image", image: image_arr }],
// 		},
// 	],
// });

// console.log(re.text);
