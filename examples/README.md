# RPG Writer

A Mastra-powered system for generating complete RPG novels with rich worldbuilding and narrative coherence.

## Overview

This example demonstrates how to create a multi-agent system using Mastra, where specialized AI agents collaborate to create a complete RPG novel:

1. **Writer Agent**: Creates immersive, detailed fantasy narratives with focus on worldbuilding
2. **Reviewer Agent**: Analyzes and provides feedback on narrative consistency, worldbuilding depth, and engagement
3. **Chapter Revision Agent**: Revises chapters based on negotiated feedback
4. **Changelist Synthesizer Agent**: Creates an actionable changelist from writer-reviewer negotiations

The system generates a complete RPG novel chapter by chapter, with a sophisticated review and negotiation process that respects both creative vision and editorial expertise.

## Features

- Interactive user input for genre, premise, title, chapter count, and desired length
- Story-specific organization with dedicated folders for each novel
- Customizable chapter length options (small, medium, large, epic)
- AI-generated story outline with world rules, character profiles, and chapter breakdown
- Advanced feedback negotiation system:
  - Initial chapter generation with target word count
  - Detailed chapter review with word count validation
  - Writer response with ability to dispute or defend creative choices
  - Reviewer-writer negotiation with up to 3 rounds of discussion
  - Creation of agreed-upon changelist
  - Final revision based only on negotiated changes while maintaining target length
- Final comprehensive review of the complete novel
- All content saved to files for easy reference

## How to Use

1. Make sure you have all dependencies installed:
   ```bash
   bun install
   ```

2. Run the example:
   ```bash
   bun examples/rpg-writer/run.ts
   ```
   or
   ```bash
   ./examples/rpg-writer/run.ts
   ```

3. Follow the prompts to specify:
   - The genre for your RPG story (e.g., fantasy, sci-fi, steampunk)
   - An optional title for your story (or let the AI generate one)
   - An optional core idea or premise
   - The number of chapters to generate
   - The desired word count range per chapter:
     - Small: 1,000-2,000 words
     - Medium: 2,000-4,000 words 
     - Large: 4,000-6,000 words
     - Epic: 6,000-10,000 words

4. The system will automatically:
   - Generate a detailed story outline
   - Create a dedicated folder for your story based on its title
   - Create each chapter according to the specified length
   - Review each chapter (including word count compliance)
   - Conduct a writer-reviewer negotiation about the feedback
   - Create a final changelist of agreed-upon modifications
   - Revise the chapter based only on the negotiated changes
   - Provide a final comprehensive review of the complete novel

All output will be saved to a story-specific directory within `examples/rpg-writer/output/`.

## Output Structure

For each story, a unique folder is created (based on the title and timestamp):

- `story_outline.md` - The complete story outline
- `chapter_N.md` - Individual chapters (revised based on negotiated feedback)
- `negotiation_N.md` - Complete logs of writer-reviewer discussions for each chapter
- `final_review.md` - Comprehensive review of the entire story

## Writer-Reviewer Negotiation Process

The RPG Writer system implements a sophisticated feedback negotiation loop for each chapter:

1. The Writer Agent creates a chapter based on the story outline, previous chapters, and target word count
2. The Reviewer Agent analyzes the chapter and provides detailed feedback, including word count assessment
3. The Writer responds to each feedback point, either accepting or disputing with reasoning
4. The Reviewer considers the Writer's perspective and responds, seeking compromise
5. This negotiation continues for up to 3 rounds or until consensus is reached
6. The Changelist Synthesizer creates a final list of agreed-upon changes
7. The Chapter Revision Agent implements only the negotiated changes while maintaining the target word count
8. The revised chapter is saved and becomes part of the story

This approach mimics real-world author-editor relationships where creative disagreements are discussed and resolved collaboratively, resulting in a better final product that preserves the author's vision while benefiting from editorial insight.

## Technical Details

The example demonstrates:
- Creating and configuring Mastra agents with specific roles in a collaborative workflow
- Using multiple AI models for different aspects of the generation process:
  - Grok-3 for creative writing
  - Gemini for review and feedback synthesis
  - Llama-4 for precise chapter revisions
- Implementing a sophisticated multi-agent negotiation process
- Balancing creative authority with editorial expertise
- Maintaining target word counts across revisions
- Organizing output into story-specific directories with proper naming conventions
- Processing and maintaining narrative consistency throughout a long-form work
- File I/O for saving generated content and negotiation logs 