import fs from "node:fs/promises";
import path from "node:path";
import { createInterface } from "node:readline/promises";
import { google } from "@ai-sdk/google";
import { groq } from "@ai-sdk/groq";
import { xai } from "@ai-sdk/xai";
import { <PERSON><PERSON> } from "@mastra/core";
import { Agent } from "@mastra/core/agent";

// Base output directory
const BASE_OUTPUT_DIR = path.join(process.cwd(), "examples/output");

// Define chapter word count options
const WORD_COUNT_OPTIONS = {
  small: { min: 1000, max: 2000, label: "Small (1,000-2,000 words)" },
  medium: { min: 2000, max: 4000, label: "Medium (2,000-4,000 words)" },
  large: { min: 4000, max: 6000, label: "Large (4,000-6,000 words)" },
  epic: { min: 6000, max: 10000, label: "Epic (6,000-10,000 words)" },
};

// Define story styles
const STORY_STYLES = {
  rpg: {
    name: "Traditional RPG",
    description: "A rich, immersive fantasy world with detailed worldbuilding and complex characters, suitable for a tabletop RPG setting.",

    writerPrompt: `
    You are a master RPG storyteller and worldbuilder who creates immersive, detailed fantasy narratives.
    
    Your specialties include:
    - Creating rich, believable worlds with consistent rules and systems
    - Developing complex characters with clear motivations and growth arcs
    - Crafting engaging plots with appropriate pacing and tension
    - Balancing exposition with action and dialogue
    - Creating magic systems, political structures, religions, and cultures that feel authentic
    
    When writing, focus on showing rather than telling. Make the world come alive through sensory details,
    character interactions, and environmental cues. Create a world that feels lived-in and believable.
    
    Your goal is to write complete chapters for an RPG novel that could serve as the basis for a game.
    Each chapter should advance the story while building the world and developing characters.
    
    IMPORTANT: Maintain consistency with previous chapters. Keep track of character details, plot points, 
    and worldbuilding elements to ensure a cohesive narrative.

    When provided with reviewer feedback, carefully evaluate each point. You should:
    1. Accept valid criticism that would improve the chapter
    2. Dispute or clarify points where you believe the reviewer has misunderstood your intent or where changes would harm the narrative
    3. Provide a clear rationale for your position on disputed points
    4. Be constructive and focus on what's best for the story

    You have creative authority but should be open to feedback that improves the work.
    
    When given a target word count range, aim to create a chapter whose length falls within that range.
    Adjust your level of detail, pace, and descriptiveness to achieve the target length without
    compromising the quality of the narrative.
  `,

    reviewerPrompt: `
    You are an expert RPG narrative designer and editor who reviews RPG stories for consistency, 
    engagement, and worldbuilding quality.
    
    Your role is to analyze chapters of an RPG novel and provide constructive feedback focused on:
    
    1. CONSISTENCY CHECK:
       - Plot holes or contradictions in the narrative
       - Character behavior inconsistent with established traits
       - Worldbuilding elements that contradict established rules
       - Timeline inconsistencies
    
    2. WORLDBUILDING ASSESSMENT:
       - Richness and depth of the world (cultures, magic systems, politics, etc.)
       - Believability and internal consistency of the world's rules
       - Balance between exposition and discovery
       - Unique elements that distinguish this world from generic fantasy
    
    3. ENGAGEMENT EVALUATION:
       - Pacing and tension
       - Character development and relatability
       - Reader investment in outcomes
       - Hooks for future chapters
    
    4. IMPROVEMENT SUGGESTIONS:
       - Specific ways to deepen worldbuilding
       - Character development opportunities
       - Plot developments that could increase reader engagement
       - Elements that could be expanded or enhanced
       
    5. WORD COUNT ASSESSMENT:
       - Check if the chapter meets the specified word count range
       - Suggest areas to expand if below target range
       - Suggest areas to trim if above target range
       - Assess if pacing issues relate to word count
    
    When reviewing, first acknowledge the strengths of the chapter, then provide constructive criticism.
    Be specific in your feedback with examples from the text. Your goal is to help make the RPG story
    more immersive, consistent, and engaging.

    IMPORTANT: Format your feedback in a way that's actionable for the writer. Break down your suggestions
    clearly with section headers and bullet points. Prioritize the most critical issues.
    
    When the writer disputes your feedback, carefully consider their perspective. You should:
    1. Concede points where the writer has a valid creative reason
    2. Clarify and defend your position on points you still believe are important
    3. Suggest compromise solutions where appropriate
    4. Always focus on what makes the best story, not on being right
  `,

    chapterRevisionPrompt: `
    You are an expert RPG story editor who specializes in revising chapters based on reviewer feedback.
    
    Your task is to take an existing chapter, reviewer comments, and a final negotiated changelist,
    then revise the chapter to address the agreed-upon points while maintaining the original story direction and author's voice.
    
    When revising, you should:
    1. Fix all consistency issues and plot holes identified in the final changelist
    2. Enhance worldbuilding elements that need more depth
    3. Improve character development as suggested
    4. Adjust pacing and tension as recommended
    5. Add elements to make the world more unique and intriguing
    
    Always maintain the core plot points and story direction, but enhance the execution based on feedback.
    You should produce a fully revised chapter that incorporates the negotiated changes
    while remaining true to the original story intent.
    
    Return the complete revised chapter, not just the changes or a summary of changes.
    
    When given a target word count range, maintain awareness of the chapter length during your revisions.
    If the original chapter was significantly shorter or longer than the target range, adjust your revisions
    to bring the final chapter closer to the target word count while addressing the changelist requirements.
  `,

    outlinePrompt: (genre: string, idea: string, totalChapters: number, storyTitle?: string) => {
      let prompt = `Create a detailed outline for an RPG novel in the ${genre} genre.`;

      if (idea && idea.length > 0) {
        prompt += ` The core idea is: ${idea}.`;
      }

      if (storyTitle && storyTitle.length > 0) {
        prompt += ` The title of the story is: "${storyTitle}".`;
      }

      prompt +=
        `\n\nYour outline should include:\n1. A clear title for the story at the beginning\n2. The high-level premise and main conflict\n3. The world setting with key locations, factions, and any magic/technology systems\n4. Main character profiles with motivations and arcs\n5. A chapter-by-chapter breakdown (for all ${totalChapters} chapters) highlighting key plot events\n6. Major worldbuilding elements that will be explored\n\nFocus on creating a rich, immersive world with internal consistency and unique elements that would make for an engaging RPG game setting.\n\nIMPORTANT: Start your outline with 'Title: [Your Story Title]' on the first line.`;

      return prompt;
    }
  },

  lightNovel: {
    name: "Light Novel",
    description: "A Japanese-style light novel with anime aesthetics, character archetypes, and narrative style that could potentially be adapted into an anime series.",

    writerPrompt: `
    You are a master light novel author who creates immersive, vibrant stories in the Japanese light novel style that could be adapted into anime.
    
    Your specialties include:
    - Creating anime-inspired worlds with distinct visual aesthetics and settings
    - Developing characters with clear archetypes and tropes common in anime, while giving them depth
    - Crafting plots with dramatic moments, emotional beats, and scene transitions suitable for visual adaptation
    - Balancing dialogue-heavy scenes with action sequences and internal monologues
    - Creating systems (magic, technology, etc.) with clear rules that would be visually interesting
    
    When writing, focus on:
    - Vivid, visual descriptions that could translate well to animation
    - Snappy, character-revealing dialogue with distinctive speech patterns
    - Character reactions and expressions that convey emotion
    - Action scenes with clear choreography
    - Plot beats that would work well as episode breaks in an anime adaptation
    
    Your goal is to write complete chapters for a light novel that feels like it could be the next popular anime adaptation.
    Each chapter should advance the story while developing characters and their relationships.
    
    IMPORTANT: Maintain consistency with previous chapters. Keep track of character details, plot points, 
    and worldbuilding elements to ensure a cohesive narrative.

    When provided with reviewer feedback, carefully evaluate each point. You should:
    1. Accept valid criticism that would improve the chapter
    2. Dispute or clarify points where you believe the reviewer has misunderstood your intent or where changes would harm the narrative
    3. Provide a clear rationale for your position on disputed points
    4. Be constructive and focus on what's best for the story

    You have creative authority but should be open to feedback that improves the work.
    
    When given a target word count range, aim to create a chapter whose length falls within that range.
    Adjust your level of detail, pace, and descriptiveness to achieve the target length without
    compromising the quality of the narrative.
  `,

    reviewerPrompt: `
    You are an expert light novel editor who reviews stories for anime adaptation potential, 
    engagement, and consistency with the light novel format.
    
    Your role is to analyze chapters of a light novel and provide constructive feedback focused on:
    
    1. CONSISTENCY CHECK:
       - Plot holes or contradictions in the narrative
       - Character behavior inconsistent with established traits or archetypes
       - Worldbuilding elements that contradict established rules
       - Timeline inconsistencies
    
    2. ANIME ADAPTATION POTENTIAL:
       - Visual appeal of scenes and settings
       - Character designs and distinguishing features
       - Action sequences that would animate well
       - Emotional moments that would resonate with viewers
       - Dialogue that feels natural when spoken aloud
    
    3. LIGHT NOVEL CONVENTIONS:
       - Appropriate use of anime tropes and character archetypes
       - Balance of dialogue, action, and internal monologue
       - Scene transitions that work in the light novel format
       - Character relationships and dynamics common in the genre
    
    4. ENGAGEMENT EVALUATION:
       - Pacing and tension
       - Character development and appeal
       - Reader investment in outcomes
       - Hooks for future chapters
       - "Cool factor" of key moments
    
    5. IMPROVEMENT SUGGESTIONS:
       - Ways to make scenes more visually striking
       - Character development opportunities
       - Dialogue improvements
       - Elements that could be expanded or enhanced
       
    6. WORD COUNT ASSESSMENT:
       - Check if the chapter meets the specified word count range
       - Suggest areas to expand if below target range
       - Suggest areas to trim if above target range
       - Assess if pacing issues relate to word count
    
    When reviewing, first acknowledge the strengths of the chapter, then provide constructive criticism.
    Be specific in your feedback with examples from the text. Your goal is to help make the light novel
    more appealing for potential anime adaptation and engaging for readers.

    IMPORTANT: Format your feedback in a way that's actionable for the writer. Break down your suggestions
    clearly with section headers and bullet points. Prioritize the most critical issues.
    
    When the writer disputes your feedback, carefully consider their perspective. You should:
    1. Concede points where the writer has a valid creative reason
    2. Clarify and defend your position on points you still believe are important
    3. Suggest compromise solutions where appropriate
    4. Always focus on what makes the best story, not on being right
  `,

    chapterRevisionPrompt: `
    You are an expert light novel editor who specializes in revising chapters based on reviewer feedback.
    
    Your task is to take an existing chapter, reviewer comments, and a final negotiated changelist,
    then revise the chapter to address the agreed-upon points while maintaining the original story direction and anime-inspired style.
    
    When revising, you should:
    1. Fix all consistency issues and plot holes identified in the final changelist
    2. Enhance visual elements and scenes to better suit potential anime adaptation
    3. Improve character development and dialogue as suggested
    4. Adjust pacing and tension as recommended
    5. Add elements to make the story more appealing to light novel readers and anime fans
    
    Always maintain the core plot points and story direction, but enhance the execution based on feedback.
    You should produce a fully revised chapter that incorporates the negotiated changes
    while remaining true to the light novel style.
    
    Return the complete revised chapter, not just the changes or a summary of changes.
    
    When given a target word count range, maintain awareness of the chapter length during your revisions.
    If the original chapter was significantly shorter or longer than the target range, adjust your revisions
    to bring the final chapter closer to the target word count while addressing the changelist requirements.
  `,

    outlinePrompt: (genre: string, idea: string, totalChapters: number, storyTitle?: string) => {
      let prompt = `Create a detailed outline for a light novel in the ${genre} genre that could potentially be adapted into an anime series.`;

      if (idea && idea.length > 0) {
        prompt += ` The core idea is: ${idea}.`;
      }

      if (storyTitle && storyTitle.length > 0) {
        prompt += ` The title of the story is: "${storyTitle}".`;
      }

      prompt +=
        `\n\nYour outline should include:\n1. A clear title for the light novel at the beginning\n2. The high-level premise and main conflict\n3. The setting with visual aesthetics and any systems (magic, technology, etc.)\n4. Main character profiles with personalities, appearances, and potential character arcs\n5. Supporting character profiles and their relationships to the main cast\n6. A chapter-by-chapter breakdown (for all ${totalChapters} chapters) highlighting key plot events\n7. Potential 'anime episode' breakdown showing how chapters could adapt to screen\n\nFocus on creating characters and scenarios that would appeal to light novel readers and translate well to animation. Consider common anime genres (isekai, slice of life, shonen, shojo, etc.) and tropes while adding your own unique elements.\n\nIMPORTANT: Start your outline with 'Title: [Your Light Novel Title]' on the first line.`;

      return prompt;
    }
  }
};

const MAX_NEGOTIATION_ROUNDS = 3; // Maximum number of back-and-forth exchanges

// Helper function to sanitize a title for filesystem use
function sanitizeForFilename(title: string): string {
  // Remove any characters that are problematic for filenames
  // Replace spaces with underscores
  return title
    .replace(/[/\\?%*:|"<>]/g, "")
    .replace(/\s+/g, "_")
    .toLowerCase()
    .substring(0, 50); // Limit length to avoid path length issues
}

// Extract title from story outline
function extractTitleFromOutline(storyOutline: string): string {
  // Try to find a title in the first few lines
  const lines = storyOutline.split('\n').slice(0, 10);

  // Look for lines that might contain a title
  for (const line of lines) {
    if (/title|name/i.test(line) && line.includes(':')) {
      const titlePart = line.split(':')[1];
      if (titlePart) {
        return titlePart.trim();
      }
    }
  }

  // If no explicit title found, use the first sentence or line
  const firstLine = lines[0]?.trim() || '';
  if (firstLine) {
    // If it's a long line, use just the first part
    if (firstLine.length > 50) {
      return firstLine.substring(0, 50).trim();
    }
    return firstLine;
  }

  // Fallback
  return "untitled_story";
}

// Ensure output directory exists
async function ensureOutputDir(storyTitle: string): Promise<string> {
  try {
    // Ensure the base output directory exists
    await fs.mkdir(BASE_OUTPUT_DIR, { recursive: true });

    // Create a sanitized folder name from the title
    const sanitizedTitle = sanitizeForFilename(storyTitle);
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-").substring(0, 19);
    const storyDir = path.join(BASE_OUTPUT_DIR, `${sanitizedTitle}_${timestamp}`);

    // Create the story-specific directory
    await fs.mkdir(storyDir, { recursive: true });

    return storyDir;
  } catch (error) {
    console.error("Error creating output directory:", error);
    process.exit(1);
  }
}

const writer_model = xai("grok-3");
const reviewer_model = google("gemini-2.0-flash-001");
const chapter_revision_model = groq("meta-llama/llama-4-scout-17b-16e-instruct");
const changelist_synthesizer_model = google("gemini-2.0-flash-001");

// Changelist synthesizer agent - creates final change requirements after negotiation
const changelistAgent = new Agent({
  name: "Changelist Synthesizer",
  instructions: `
    You are an expert at synthesizing discussions between writers and editors to produce a clear, 
    actionable changelist for story revisions.
    
    Your task is to analyze the conversation between a writer and reviewer, then create a final
    changelist that reflects the consensus reached during their discussion.
    
    When creating the changelist:
    1. Include only points that both parties eventually agreed should be changed
    2. Leave out suggested changes that the writer successfully disputed
    3. Include compromise solutions that were reached during the discussion
    4. Format the changelist in clear, actionable items organized by category
    
    The changelist should be structured, specific, and represent a fair resolution of the
    negotiation process. It will be used directly by the revision agent to improve the chapter.
  `,
  model: changelist_synthesizer_model,
});

// Initialize Mastra with agents that will be set dynamically based on style selection
let mastra: Mastra;

// Helper to save a chapter to a file
async function saveChapter(storyDir: string, chapterNumber: number, content: string) {
  const filePath = path.join(storyDir, `chapter_${chapterNumber}.md`);
  await fs.writeFile(filePath, content, "utf-8");
  console.log(`Chapter ${chapterNumber} saved to ${filePath}`);
  return filePath;
}

// Helper to save story outline
async function saveOutline(storyDir: string, outline: string) {
  const filePath = path.join(storyDir, "story_outline.md");
  await fs.writeFile(filePath, outline, "utf-8");
  console.log(`Story outline saved to ${filePath}`);
  return filePath;
}

// Helper to read all previous chapters
async function getAllChapters(storyDir: string) {
  const files = await fs.readdir(storyDir);
  const chapterFiles = files
    .filter((file) => file.startsWith("chapter_") && file.endsWith(".md"))
    .sort((a, b) => {
      const numA = Number.parseInt(a.split("_")[1]?.split(".")[0] ?? "0");
      const numB = Number.parseInt(b.split("_")[1]?.split(".")[0] ?? "0");
      return numA - numB;
    });

  const chapters: { number: number; content: string }[] = [];
  for (const file of chapterFiles) {
    const chapterNumber = Number.parseInt(file.split("_")[1]?.split(".")[0] ?? "0");
    const content = await fs.readFile(path.join(storyDir, file), "utf-8");
    chapters.push({ number: chapterNumber, content });
  }

  return chapters;
}

// Helper functions for context management

// Function to prepare full context for agents
function prepareFullContext(previousChapters: { number: number; content: string }[], maxChaptersToPrepend = 3) {
  // If there are many previous chapters, we might need to include a summary for the older ones
  // and full content for the most recent ones to balance context and token limits

  if (previousChapters.length === 0) {
    return "";
  }

  let context = "PREVIOUS CHAPTERS:\n\n";

  // If we have lots of chapters, provide summaries for older ones
  if (previousChapters.length > maxChaptersToPrepend) {
    const olderChapters = previousChapters.slice(0, previousChapters.length - maxChaptersToPrepend);
    context += "EARLIER CHAPTERS SUMMARY:\n";

    for (const chapter of olderChapters) {
      // Just provide a summary for older chapters
      const summary = chapter.content.substring(0, 800);
      context += `Chapter ${chapter.number}: ${summary}...\n\n`;
    }

    context += "RECENT CHAPTERS (FULL CONTENT):\n\n";

    // Provide full content for the most recent chapters
    const recentChapters = previousChapters.slice(previousChapters.length - maxChaptersToPrepend);
    for (const chapter of recentChapters) {
      context += `===== CHAPTER ${chapter.number} =====\n\n${chapter.content}\n\n`;
    }
  } else {
    // If we have few enough chapters, include them all in full
    for (const chapter of previousChapters) {
      context += `===== CHAPTER ${chapter.number} =====\n\n${chapter.content}\n\n`;
    }
  }

  return context;
}

// Generate a chapter
async function generateChapter(
  chapterNumber: number,
  previousChapters: { number: number; content: string }[],
  storyOutline: string,
  genre: string,
  wordCount: { min: number; max: number }
) {
  let prompt = `You are writing Chapter ${chapterNumber} of a ${genre} story.\n\n`;

  prompt += `Overall story outline:\n${storyOutline}\n\n`;

  // Include full context from previous chapters
  if (previousChapters.length > 0) {
    prompt += prepareFullContext(previousChapters);
  }

  prompt += `Now, write Chapter ${chapterNumber} in full. Make it engaging, descriptive, and focus on advancing both the plot and worldbuilding.\n\n`;
  prompt += `TARGET WORD COUNT: Your chapter should be between ${wordCount.min} and ${wordCount.max} words. Adjust your level of detail and descriptiveness accordingly while maintaining narrative quality.`;

  const response = await mastra.getAgent("writerAgent").generate(prompt);
  return response.text;
}

// Review a single chapter
async function reviewChapter(
  chapterNumber: number,
  chapterContent: string,
  previousChapters: { number: number; content: string }[],
  storyOutline: string,
  genre: string,
  wordCount: { min: number; max: number }
) {
  // Count words in the chapter (simple approximation)
  const wordCountEstimate = chapterContent.split(/\s+/).length;
  const isWithinRange = wordCountEstimate >= wordCount.min && wordCountEstimate <= wordCount.max;

  let prompt = `You are reviewing Chapter ${chapterNumber} of a ${genre} story. Here is the overall story outline:\n${storyOutline}\n\n`;

  // Include full context from previous chapters
  if (previousChapters.length > 0) {
    prompt += prepareFullContext(previousChapters);
  }

  prompt += `\n===== CURRENT CHAPTER FOR REVIEW =====\n${chapterContent}\n\n`;

  prompt += `WORD COUNT INFORMATION:
- Target word count range: ${wordCount.min} to ${wordCount.max} words
- Current estimated word count: ${wordCountEstimate} words
- Status: ${isWithinRange ? "Within target range" : wordCountEstimate < wordCount.min ? "Below target range" : "Above target range"}
`;

  prompt +=
    "Please provide a constructive review of this chapter, focusing on:\n" +
    "1. Consistency issues with previous chapters or the story outline\n" +
    "2. Worldbuilding elements that could be enhanced\n" +
    "3. Character development opportunities\n" +
    "4. Plot and pacing improvements\n" +
    "5. Word count assessment (if not within target range, suggest specific areas to expand or trim)\n" +
    "6. Specific suggestions to make the chapter more engaging and immersive\n\n" +
    "Format your feedback with clear sections and actionable bullet points. Be specific and provide concrete examples from the text.";

  const response = await mastra.getAgent("reviewerAgent").generate(prompt);
  return response.text;
}

// Writer response to reviewer feedback
async function writerRespondToFeedback(
  chapterNumber: number,
  chapterContent: string,
  reviewFeedback: string,
  storyOutline: string
) {
  const prompt = `
    As the writer of Chapter ${chapterNumber}, you've received the following feedback from your reviewer:

    REVIEWER FEEDBACK:
    ${reviewFeedback}

    Please respond to this feedback, addressing each major point. You should:
    1. Acknowledge feedback points you agree with
    2. Respectfully dispute or clarify points where you disagree
    3. Explain your reasoning for any disputed points
    4. Focus on what's best for the story

    Format your response by clearly referencing each section of the reviewer's feedback.
  `;

  const response = await mastra.getAgent("writerAgent").generate(prompt);
  return response.text;
}

// Reviewer response to writer's disputes
async function reviewerRespondToWriter(
  writerResponse: string,
  originalFeedback: string,
  chapterContent: string
) {
  const prompt = `
    As the reviewer, you provided the following feedback:

    ORIGINAL REVIEW FEEDBACK:
    ${originalFeedback}

    The writer has responded with the following comments:

    WRITER'S RESPONSE:
    ${writerResponse}

    Please respond to the writer's points, especially where they've disputed your feedback.
    1. Acknowledge where the writer has valid creative reasons
    2. Clarify and defend your position on important points
    3. Suggest compromise solutions where appropriate
    4. Focus on what makes the best story

    Format your response by addressing each of the writer's main points.
  `;

  const response = await mastra.getAgent("reviewerAgent").generate(prompt);
  return response.text;
}

// Create a final changelist after negotiation
async function createFinalChangelist(negotiationConversation: string, chapterContent: string) {
  const prompt = `
    Below is a negotiation conversation between a writer and reviewer about revisions to a chapter:

    ${negotiationConversation}

    Based on this conversation, create a final actionable changelist that reflects what both parties
    agreed should be modified. Include only points that were ultimately accepted or compromised on,
    and exclude suggestions that were successfully disputed by the writer.

    Format the changelist as a clear, structured list of specific changes organized by category
    (consistency, worldbuilding, character development, etc.).
  `;

  const response = await mastra.getAgent("changelistAgent").generate(prompt);
  return response.text;
}

// Run the feedback negotiation process
async function negotiateFeedback(
  storyDir: string,
  chapterNumber: number,
  chapterContent: string,
  initialReview: string,
  storyOutline: string
) {
  console.log(`\nStarting feedback negotiation for Chapter ${chapterNumber}...`);

  let negotiationHistory = `
INITIAL REVIEW:
${initialReview}

`;

  // First writer response
  console.log("Writer evaluating and responding to feedback...");
  const writerResponse = await writerRespondToFeedback(
    chapterNumber,
    chapterContent,
    initialReview,
    storyOutline
  );

  negotiationHistory += `
WRITER RESPONSE:
${writerResponse}

`;

  // Track conversation
  let currentWriterResponse = writerResponse;
  let currentReviewerResponse = initialReview;

  // Allow up to MAX_NEGOTIATION_ROUNDS of back and forth
  for (let round = 0; round < MAX_NEGOTIATION_ROUNDS - 1; round++) {
    console.log(`Negotiation round ${round + 1}/${MAX_NEGOTIATION_ROUNDS}...`);

    // Reviewer responds to writer
    console.log("Reviewer considering writer's points...");
    const reviewerResponse = await reviewerRespondToWriter(
      currentWriterResponse,
      currentReviewerResponse,
      chapterContent
    );

    negotiationHistory += `
REVIEWER RESPONSE (ROUND ${round + 1}):
${reviewerResponse}

`;

    // Check if further negotiation is needed
    if (reviewerResponse.toLowerCase().includes("agree with all") ||
      reviewerResponse.toLowerCase().includes("accept all") ||
      round === MAX_NEGOTIATION_ROUNDS - 2) {
      break;
    }

    // Writer responds again
    console.log("Writer responding to reviewer's latest points...");
    currentWriterResponse = await writerRespondToFeedback(
      chapterNumber,
      chapterContent,
      reviewerResponse,
      storyOutline
    );

    negotiationHistory += `
WRITER RESPONSE (ROUND ${round + 2}):
${currentWriterResponse}

`;

    currentReviewerResponse = reviewerResponse;
  }

  // Generate final changelist
  console.log("Creating final changelist based on negotiation...");
  const finalChangelist = await createFinalChangelist(negotiationHistory, chapterContent);

  negotiationHistory += `
FINAL AGREED CHANGELIST:
${finalChangelist}
`;

  // Save the negotiation for reference (optional)
  await fs.writeFile(
    path.join(storyDir, `negotiation_${chapterNumber}.md`),
    negotiationHistory,
    "utf-8"
  );

  return {
    negotiationHistory,
    finalChangelist
  };
}

// Revise a chapter based on negotiated changelist
async function reviseChapter(
  chapterNumber: number,
  originalContent: string,
  changelist: string,
  storyOutline: string,
  genre: string,
  wordCount: { min: number; max: number },
  previousChapters: { number: number; content: string }[]
) {
  let prompt = `
    As a story editor, you need to revise Chapter ${chapterNumber} based on the agreed changelist.
    
    STORY OUTLINE:
    ${storyOutline.substring(0, 1000)}...
  `;

  // Include full context from previous chapters
  if (previousChapters.length > 0) {
    prompt += "\n\nPREVIOUS CHAPTERS CONTEXT:\n";
    prompt += prepareFullContext(previousChapters);
  }

  prompt += `
    ORIGINAL CHAPTER ${chapterNumber}:
    ${originalContent}
    
    AGREED CHANGELIST (after writer-reviewer negotiation):
    ${changelist}
    
    TARGET WORD COUNT: Your revised chapter should be between ${wordCount.min} and ${wordCount.max} words.
    
    Please revise the entire chapter to address the agreed-upon changes while maintaining the original story direction.
    Focus ONLY on implementing the changes in the final changelist, as these represent what both the writer and reviewer
    have agreed to modify.
    
    Make adjustments as needed to bring the chapter length closer to the target word count range, while
    preserving the essence of the original and implementing the required changes.
    
    Return the complete revised chapter with all improvements incorporated.
  `;

  const response = await mastra.getAgent("chapterRevisionAgent").generate(prompt);
  return response.text;
}

// Review all chapters for final review
async function reviewStory(chapters: { number: number; content: string }[], storyOutline: string, genre: string) {
  let prompt = `You are reviewing a ${genre} story. Here is the overall story outline:\n${storyOutline}\n\n`;

  prompt += "ALL CHAPTERS:\n";
  for (const chapter of chapters) {
    prompt += `\n===== CHAPTER ${chapter.number} =====\n${chapter.content}\n\n`;
  }

  prompt +=
    "Please provide a comprehensive review of the complete story, focusing on:\n" +
    "1. Overall narrative arc and cohesion\n" +
    "2. Worldbuilding depth and consistency\n" +
    "3. Character development throughout the story\n" +
    "4. Plot engagement and pacing across chapters\n" +
    "5. Strengths of the story\n" +
    "6. Potential areas for expansion in future volumes\n\n" +
    "This is a final review of the complete work, so focus on the story as a whole rather than individual chapter issues.";

  const response = await mastra.getAgent("reviewerAgent").generate(prompt);
  return response.text;
}

// Generate a story outline based on genre/idea and style
async function generateStoryOutline(genre: string, idea: string, styleConfig: typeof STORY_STYLES.rpg | typeof STORY_STYLES.lightNovel, totalChapters: number, storyTitle?: string) {
  const prompt = styleConfig.outlinePrompt(genre, idea, totalChapters, storyTitle);
  const response = await mastra.getAgent("writerAgent").generate(prompt);
  return response.text;
}

// Create agents with appropriate prompts based on selected style
function createAgents(styleConfig: typeof STORY_STYLES.rpg | typeof STORY_STYLES.lightNovel) {
  // Writer agent
  const writerAgent = new Agent({
    name: "Writer",
    instructions: styleConfig.writerPrompt,
    model: writer_model,
  });

  // Reviewer agent
  const reviewerAgent = new Agent({
    name: "Story Reviewer",
    instructions: styleConfig.reviewerPrompt,
    model: reviewer_model,
  });

  // Chapter revision agent
  const chapterRevisionAgent = new Agent({
    name: "Chapter Revision Agent",
    instructions: styleConfig.chapterRevisionPrompt,
    model: chapter_revision_model,
  });

  // Initialize Mastra with appropriate agents
  mastra = new Mastra({
    agents: { writerAgent, reviewerAgent, chapterRevisionAgent, changelistAgent },
  });
}

// Main function
async function main() {
  // Set up readline interface for user input
  const rl = createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  console.log("=== Story Generator ===");
  console.log("This system will generate a complete story chapter by chapter.");

  // Get all user inputs first before starting generation

  // Get style selection
  console.log("\nSelect story style:");
  console.log(`1. ${STORY_STYLES.rpg.name} - ${STORY_STYLES.rpg.description}`);
  console.log(`2. ${STORY_STYLES.lightNovel.name} - ${STORY_STYLES.lightNovel.description}`);

  const styleOption = await rl.question("Enter your choice (1-2) [1]: ");
  const styleSelection = styleOption.trim() || "1";
  const selectedStyle = styleSelection === "2" ? STORY_STYLES.lightNovel : STORY_STYLES.rpg;

  console.log(`\nSelected style: ${selectedStyle.name}`);

  // Set up agents based on selected style
  createAgents(selectedStyle);

  // Get genre from user or default
  let genre = await rl.question("What genre would you like for your story? (e.g., fantasy, sci-fi, steampunk, horror) [fantasy]: ");
  genre = genre.trim() || "fantasy";

  // Get optional story title
  const titleInput = await rl.question("Provide an optional title for your story (press Enter to let AI decide): ");
  const userProvidedTitle = titleInput.trim();

  // Get optional core idea
  const ideaInput = await rl.question("Provide an optional core idea or premise for your story (press Enter to let AI decide): ");
  const idea = ideaInput.trim();

  // Get number of chapters
  const chaptersStr = await rl.question("How many chapters would you like to generate? [10]: ");
  const totalChapters = Number.parseInt(chaptersStr.trim() || "10");

  // Get desired word count per chapter
  console.log("\nSelect chapter length:");
  Object.entries(WORD_COUNT_OPTIONS).forEach(([key, option], index) => {
    console.log(`${index + 1}. ${option.label}`);
  });

  const wordCountOptionStr = await rl.question("Enter your choice (1-4) [2 - Medium]: ");
  const wordCountOption = wordCountOptionStr.trim() || "2";
  const wordCountIndex = Number.parseInt(wordCountOption) - 1;

  // Default to medium if invalid choice
  const wordCountKey = Object.keys(WORD_COUNT_OPTIONS)[
    wordCountIndex >= 0 && wordCountIndex < Object.keys(WORD_COUNT_OPTIONS).length
      ? wordCountIndex
      : 1
  ] as keyof typeof WORD_COUNT_OPTIONS;

  const wordCount = WORD_COUNT_OPTIONS[wordCountKey];

  console.log(`\nSelected word count: ${wordCount.label}`);
  console.log(`Will generate ${totalChapters} chapters in the ${genre} genre with ${selectedStyle.name} style.`);

  // Now generate the story outline
  console.log("\nGenerating story outline...");
  const storyOutline = await generateStoryOutline(genre, idea, selectedStyle, totalChapters, userProvidedTitle);

  console.log("\n=== Story Outline ===\n");
  console.log(storyOutline);
  console.log("\n=====================\n");

  // Extract a title from the outline or use user-provided title
  const storyTitle = userProvidedTitle || extractTitleFromOutline(storyOutline);
  console.log(`Story title: "${storyTitle}"`);

  // Create story directory
  const storyDir = await ensureOutputDir(storyTitle);
  console.log(`Story will be saved in: ${storyDir}`);

  // Save the outline
  await saveOutline(storyDir, storyOutline);

  // Generate chapters
  for (let i = 1; i <= totalChapters; i++) {
    console.log(`\nGenerating Chapter ${i}...`);
    const previousChapters = await getAllChapters(storyDir);

    // Initial draft
    const chapterContent = await generateChapter(i, previousChapters, storyOutline, genre, wordCount);
    console.log(`Saving initial draft of Chapter ${i}...`);
    await saveChapter(storyDir, i, chapterContent);

    // Review chapter
    console.log(`\nReviewing Chapter ${i}...`);
    const reviewFeedback = await reviewChapter(i, chapterContent, previousChapters, storyOutline, genre, wordCount);

    // Negotiate feedback between writer and reviewer
    const { finalChangelist } = await negotiateFeedback(storyDir, i, chapterContent, reviewFeedback, storyOutline);

    // Revise chapter based on negotiated changelist
    console.log(`\nRevising Chapter ${i} based on negotiated changelist...`);
    const revisedChapterContent = await reviseChapter(i, chapterContent, finalChangelist, storyOutline, genre, wordCount, previousChapters);

    // Overwrite the initial chapter with the revised version
    console.log(`Overwriting Chapter ${i} with revised version...`);
    await saveChapter(storyDir, i, revisedChapterContent);
    console.log(`Chapter ${i} has been finalized.`);
  }

  // Final review of the complete story
  console.log("\nGenerating final comprehensive review...");
  const allChapters = await getAllChapters(storyDir);
  const finalReview = await reviewStory(allChapters, storyOutline, genre);

  console.log("\n=== Final Story Review ===\n");
  console.log(finalReview);
  console.log("\n==========================\n");

  // Save final review
  await fs.writeFile(path.join(storyDir, "final_review.md"), finalReview, "utf-8");

  console.log(`\nComplete ${selectedStyle.name} story has been generated in ${storyDir}`);
  console.log("You can find each chapter as a separate file with the final review document.");
  console.log("Negotiation histories are saved in 'negotiation_N.md' files for reference.");

  rl.close();
}

// Run the main function
main().catch((error) => {
  console.error("Error running story generator:", error);
  process.exit(1);
});
