#!/usr/bin/env bun
/**
 * RPG Writer Example Runner
 * Executes the RPG writer system to generate a complete novel with chapters and reviews
 */

import { spawn } from "node:child_process";
import path from "node:path";

// Get the absolute path to the RPG writer script
const scriptPath = path.join(process.cwd(), "examples/rpg-writer/rpg-writer.ts");

console.log("Starting RPG Writer example...");
console.log("==============================");

// Execute the script with Bun
const childProcess = spawn("bun", ["run", scriptPath], {
	stdio: "inherit",
});

// Handle process exit
childProcess.on("exit", (code) => {
	if (code === 0) {
		console.log("RPG Writer completed successfully!");
	} else {
		console.error(`RPG Writer exited with code ${code}`);
	}
	process.exit(code ?? 1);
});

// Handle errors
childProcess.on("error", (error) => {
	console.error("Error running RPG Writer:", error);
	process.exit(1);
});
