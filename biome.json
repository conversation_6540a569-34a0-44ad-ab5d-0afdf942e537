{"$schema": "https://biomejs.dev/schemas/2.0.6/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "includes": ["**"]}, "formatter": {"enabled": true, "lineWidth": 220, "indentStyle": "tab"}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noNonNullAssertion": "off", "noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error"}, "suspicious": {"useAwait": "error"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}}