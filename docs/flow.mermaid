flowchart TD
    %% ─────────────────────────────
    %% MAIN LINEAR PROGRESSION
    %% ─────────────────────────────
    World[WORLD]
    Map[MAP]
    Json[JSON&nbsp;FORMAT]
    Graph[GRAPH]
    Mini[MINI&nbsp;MAPS&nbsp;FOR&nbsp;REGION]
    NPCs[NPCs&nbsp;FOR&nbsp;REGION]
    Interact[PLAYER&nbsp;INTERACTION<br/>LEADS&nbsp;TO&nbsp;QUESTS]
    Story[STORY&nbsp;QUEST&nbsp;+&nbsp;REWARDS]
    UnlockRegion[UNLOCK&nbsp;NEW&nbsp;REGION]
    Crypt[UNLOCK&nbsp;CRYPT]
    Finale[FINALE&nbsp;STORY&nbsp;QUESTS]

    World --> Map --> <PERSON><PERSON> --> Graph --> Mini --> NPCs --> Interact --> Story --> UnlockRegion --> Crypt --> Finale

    %% ─────────────────────────────
    %% REGIONAL LOOP & SIDE‑QUESTS
    %% ─────────────────────────────
    %% Self‑loop for the endless “grind” inside a region
    Interact -.->|repeatable<br/>side‑quests| Interact

    %% Loop back when a region is cleared
    UnlockRegion -->|next&nbsp;region| Mini

    %% ─────────────────────────────
    %% END‑GAME BRANCH
    %% ─────────────────────────────
    NewWorld[GEN&nbsp;NEW&nbsp;WORLD]
    Keep[KEEP&nbsp;PLAYING]
    Chaos[PURE&nbsp;CHAOS&nbsp;MODE]

    Finale --> NewWorld
    Finale --> Keep --> Chaos

    %% Restart arrow
    NewWorld -->|fresh&nbsp;seed| World
