import { z } from "zod";
import type { Character } from "../orchestrator/types";

export async function fromFile<T>(path: string): Promise<T> {
	const file = Bun.file(path);
	const exists = await file.exists();

	if (!exists) {
		throw new Error(`File ${path} does not exist`);
	}

	const json = await file.json();

	return json as T;
}

export async function getCharacterFromFile(path: string) {
	const character = await fromFile<Character>(path);
	const validated = validate<PERSON>haracter(character);
	if (!validated.success) {
		throw new Error(`Invalid character file: ${path}`);
	}
	return character;
}

function validate<PERSON><PERSON>cter(character: Character) {
	const schema = z.object({
		name: z.string(),
		system_prompt: z.string(),
	});

	return schema.safeParse(character);
}

export function formatCharacterName(name: string) {
	return name.toLowerCase().replace(/ /g, "_");
}

export function getAgentOptions(playerId: string, worldId: string, character: Character) {
	return {
		resourceId: playerId,
		threadId: `${worldId}:${formatCharacterName(character.name)}`,
	};
}
