import type { Agent, CoreMessage, Metric } from "@mastra/core";
import type { ToolsInput } from "@mastra/core/agent";
import type { RuntimeContext } from "@mastra/core/runtime-context";
import { type GenerateTextResult, generateText, type LanguageModelV1, type ToolSet } from "ai";
import { ollama } from "ollama-ai-provider";
import type z from "zod";

export function parsePostgresConnectionString(connectionString: string) {
	// Handles connection strings with or without port, e.g.:
	// ***************************************************
	// **********************************************
	const regex = /^postgresql:\/\/([^:]+):([^@]+)@([^:/]+)(?::(\d+))?\/([^?]+)\?sslmode=require/;
	const match = connectionString.match(regex);

	if (!match) {
		throw new Error("Invalid connection string");
	}

	const [, user, password, host, port, database] = match;

	if (!host || !database || !user || !password) {
		throw new Error("Invalid connection string");
	}

	return {
		host,
		port: (port ? Number.parseInt(port, 10) : undefined)!,
		database,
		user,
		password,
	};
}

/**
 * Merges all markdown files in a directory into a single markdown file
 *
 * @param sourceDir Directory containing markdown files to merge
 * @param outputFilePath Path where the merged markdown file should be saved
 * @param options Configuration options for the merge
 * @returns Promise that resolves to the path of the merged file
 */
export async function mergeMarkdownFilesFromDirectory(
	sourceDir: string,
	outputFilePath: string,
	options: {
		/**
		 * Sort files before merging. If true, files are sorted alphabetically by filename.
		 * If a function is provided, it's used as the sort comparison function.
		 * Default is true.
		 */
		sortFiles?: boolean | ((a: string, b: string) => number);
		/**
		 * Filter function to determine which files to include in the merge.
		 * By default, includes all .md files.
		 */
		fileFilter?: (filename: string) => boolean;
		/**
		 * Text to insert between the content of each file.
		 * Default is two newlines.
		 */
		separator?: string;
		/**
		 * Optional frontmatter to add at the top of the merged file.
		 */
		frontmatter?: string;
		/**
		 * Whether to include filenames as headers before each file's content.
		 * Default is false.
		 */
		includeFilenamesAsHeaders?: boolean;
		/**
		 * Header level for filenames when includeFilenamesAsHeaders is true.
		 * Default is 2 (## Filename).
		 */
		filenameHeaderLevel?: number;
	} = {},
): Promise<string> {
	// Import modules using dynamic import to avoid issues with ESM/CJS
	// compatibility in different environments
	const fs = await import("node:fs/promises");
	const path = await import("node:path");

	// Set default options
	const { sortFiles = true, fileFilter = (filename: string) => filename.endsWith(".md"), separator = "\n\n", frontmatter = "", includeFilenamesAsHeaders = false, filenameHeaderLevel = 2 } = options;

	// Get all files in the directory
	const files = await fs.readdir(sourceDir);

	// Filter for markdown files only
	const markdownFiles = files.filter(fileFilter);

	// Sort files if required
	if (sortFiles) {
		if (typeof sortFiles === "function") {
			markdownFiles.sort(sortFiles);
		} else {
			markdownFiles.sort();
		}
	}

	// Read content from each file
	const contents: string[] = [];

	for (const file of markdownFiles) {
		const filePath = path.join(sourceDir, file);
		const stats = await fs.stat(filePath);

		// Skip directories
		if (stats.isDirectory()) continue;

		const content = await fs.readFile(filePath, "utf-8");

		if (includeFilenamesAsHeaders) {
			// Remove file extension for header
			const filenameWithoutExt = file.replace(/\.[^/.]+$/, "");
			// Create header with specified level
			const headerPrefix = "#".repeat(filenameHeaderLevel);
			contents.push(`${headerPrefix} ${filenameWithoutExt}\n\n${content}`);
		} else {
			contents.push(content);
		}
	}

	// Create merged content
	let mergedContent = frontmatter;
	if (frontmatter && !frontmatter.endsWith("\n\n")) {
		mergedContent += "\n\n";
	}

	// Join all content with the specified separator
	mergedContent += contents.join(separator);

	// Create directory for output file if it doesn't exist
	const outputDir = path.dirname(outputFilePath);
	await fs.mkdir(outputDir, { recursive: true });

	// Write merged content to output file
	await fs.writeFile(outputFilePath, mergedContent, "utf-8");

	return outputFilePath;
}

export function mergeMarkdowns(markdown_texts: string[]) {
	// add them all to one text string
	const all_text = markdown_texts.join("\n\n");

	// return the text
	return all_text;
}

export function get_memory_embedder_model() {
	return ollama.embedding("nomic-embed-text");

	// if (os.platform() === "win32") {
	// 	return ollama.embedding("nomic-embed-text");
	// }

	// return undefined;
}

export function parseZodObjectFromText<TResponse extends z.ZodTypeAny>(zodSchema: TResponse, text: string): z.infer<TResponse> | undefined {
	try {
		const matches = text.match(/{[\s\S]*}/g);
		console.log(matches);

		const json = matches?.[0];
		if (!json) {
			throw new Error("No JSON found in response");
		}

		const jsonObject = JSON.parse(json);
		console.log(jsonObject);
		const zodObj = zodSchema.parse(jsonObject);
		return zodObj as z.infer<TResponse>;
	} catch (error) {
		console.error("Failed to parse JSON:", error);
		return undefined;
	}
}

export function clean_name(name: string) {
	return name.replace(/ /g, "_").toLowerCase();
}

export async function generate<AgentModel extends Agent<any, any, Record<string, Metric>>, RC extends RuntimeContext<any>>(
	agent: AgentModel,
	runtimeContext: RC,
	message: CoreMessage,
): Promise<GenerateTextResult<ToolSet, never>> {
	const model = agent.getModel({
		runtimeContext: runtimeContext as RuntimeContext<unknown>,
	}) as LanguageModelV1;

	const response = await generateText({
		model,
		messages: [{ role: "system", content: await agent.getInstructions({ runtimeContext: runtimeContext as RuntimeContext<unknown> }) }, message],
	});

	return response;
}
