import type { CoreMessage, Message } from "ai";
import { z } from "zod";

// Base schemas
export const EntityIDSchema = z.string();

export const BiomeSchema = z.object({
	description: z.string(),
	unlock_lvl: z.number(),
});

export const PlaceSchema = z.object({ description: z.string(), type: z.string() });

export const LocationSchema = z.object({
	description: z.string(),
	biome: z.string(),
	unlock_lvl: z.number(),
	neighbors: z.set(z.string()),
	places: z.record(z.string(), PlaceSchema),
});

export const RelationshipSchema = z.object({
	target: EntityIDSchema,
	attitude: z.number(),
	trust: z.number(),
	familiarity: z.number(),
});

export const ItemSchema = z.object({
	name: z.string(),
	image: z.string(),
	description: z.string(),
	quantity: z.number(),
	price: z.number(),
});

export const InventorySchema = z.object({
	items: z.array(ItemSchema),
	currency: z.number(),
});

export const PlayerSchema = z.object({
	username: z.string(),
	inventory: InventorySchema,
	location: z.string(),
	unlock_lvl: z.number(),
	relationships: z.array(RelationshipSchema),
});

export const GameStateSchema = z.object({
	player: PlayerSchema,
});

export const NpcSchema = z.object({
	name: z.string(),
	core: z.string(),
	system_prompt: z.string(),
	location: LocationSchema,
});

export const CharacterSchema = z.object({
	name: z.string(),
	aliases: z.array(z.string()),
	role: z.string(),
	race: z.string(),
	personality: z.object({
		traits: z.array(z.string()),
	}),
	system_prompt: z.string(),
});

export const createWorldBiomesAndLocationsSchema = z.object({
	connections: z.record(z.string(), z.array(z.string())),
	biomes: z.record(z.string(), z.record(z.string(), z.string())),
});

export const createWorldBiomesAndLocationsReturnSchema = createWorldBiomesAndLocationsSchema.extend({
	final_location: z.string(),
});

export const worldConfigSchema = z.object({
	title: z.string(),
	genre: z.string(),
	lore: z.object({
		history: z.string(),
		cultures: z.string(),
		current_conflicts: z.string(),
		mysteries: z.string(),
	}),
});

// Messages schema - union type for the complex Messages type
export const MessagesSchema = z.union([z.string(), z.array(z.string()), z.array(z.custom<CoreMessage>()), z.array(z.custom<Message>())]);

// Infer TypeScript types from Zod schemas
export type Messages = z.infer<typeof MessagesSchema>;
export type EntityID = z.infer<typeof EntityIDSchema>;
export type Biome = z.infer<typeof BiomeSchema>;
export type Location = z.infer<typeof LocationSchema>;
export type Player = z.infer<typeof PlayerSchema>;
export type Relationship = z.infer<typeof RelationshipSchema>;
export type Inventory = z.infer<typeof InventorySchema>;
export type Item = z.infer<typeof ItemSchema>;
export type GameState = z.infer<typeof GameStateSchema>;
export type Npc = z.infer<typeof NpcSchema>;
export type Character = z.infer<typeof CharacterSchema>;
export type Place = z.infer<typeof PlaceSchema>;

export type WorldConfig = z.infer<typeof worldConfigSchema>;
export type CreateWorldBiomesAndLocationsReturn = z.infer<typeof createWorldBiomesAndLocationsReturnSchema>;
