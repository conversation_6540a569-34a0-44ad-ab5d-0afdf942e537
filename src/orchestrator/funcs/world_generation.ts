import path from "node:path";
import { RuntimeContext } from "@mastra/core/runtime-context";
import { createStep, createWorkflow } from "@mastra/core/workflows";
import { z } from "zod";
import { save_world, world_schema } from "@/db/world";
import { clean_name, generate, parseZodObjectFromText } from "@/utils/helpers";

import {
	create_map_graph_and_biomes,
	createWorldBiomesAndLocationsReturnSchema,
	createWorldBiomesAndLocationsSchema,
	map_graph_result_schema,
	PlaceSchema,
	type WorldConfig,
	worldConfigSchema,
} from "../../orchestrator";

import type { WorldAgentRuntimeContext } from "@/mastra/agents/world";
import { save_playthrough } from "@/db/playthrough";

export async function create_locations(mastra: Mastra) {
    const world = mastra.getAgent("world-agent");
    const runtimeContext = new RuntimeContext<WorldAgentRuntimeContext>();
    runtimeContext.set("type", "analyze-world-location");

    const location_response = await generate(world, runtimeContext, {
        role: "user",
        content: [{ type: "image", image: starting_location_image }],
    });
}