// import biomes from "@server/assets/jsons/biomes.json" assert { type: "json" };
// import neighbours from "@server/assets/jsons/neighbors.json" assert { type: "json" };

import z from "zod";
import {
  type Biome,
  BiomeSchema,
  type Location,
  LocationSchema,
  type Place,
} from "../types";

/** initial
 * 1. From the lore and main system prompt, we generate the map of the world as an image
 * 2. we then generate a list of places where the player can set foot on
 * 3. we get the json of the places with their neighbors
 * 4. we get the json of the unique biomes with the places that belong to them
 * 5. we get the starting place for the player
 * 6. store the new state
 */

// world_agent.create_map_input(lore, map_image)
// world_agent.create_quest_from_lore(lore)
// npc_agent.from(sylv).

export const map_graph_result_schema = z.object({
  locations: z.record(z.string(), LocationSchema),
  biomes: z.record(z.string(), BiomeSchema),
});

export type MapGraphResult = z.infer<typeof map_graph_result_schema>;

export function create_map_graph_and_biomes(
  locationsWithNeighbors: Record<string, string[]>,
  biomesWithLocations: Record<string, Record<string, string>>,
  starting_location: string,
  final_location: string,
) {
  const locationKeys: string[] = Object.keys(locationsWithNeighbors);
  const biomeKeys: string[] = Object.keys(biomesWithLocations);

  const initial_locations: Record<string, Location> = {};
  const initial_biomes: Record<string, Biome> = {};

  // add the initial locations(and their biomes) that are present in both biomes and neighbors
  for (let i = 0; i < biomeKeys.length; ++i) {
    const locationKeys = Object.keys(biomesWithLocations[biomeKeys[i]]);
    for (let j = 0; j < locationKeys.length; ++j) {
      if (
        locationKeys[j] in locationsWithNeighbors &&
        !(locationKeys[j] in initial_locations)
      ) {
        if (!(biomeKeys[i] in initial_biomes)) {
          initial_biomes[biomeKeys[i]] = {
            description: biomesWithLocations[biomeKeys[i]][locationKeys[j]],
            unlock_lvl: -1,
          };
        }

        initial_locations[locationKeys[j]] = {
          description: biomesWithLocations[biomeKeys[i]][locationKeys[j]],
          biome: biomeKeys[i],
          unlock_lvl: -1,
          neighbors: new Set<string>(),
          places: {},
        };
      }
    }
  }

  // add and mirror neighbours
  for (let i = 0; i < locationKeys.length; ++i) {
    if (locationKeys[i] in initial_locations) {
      const neighbors = locationsWithNeighbors[locationKeys[i]];
      for (let j = 0; j < neighbors.length; ++j) {
        if (
          neighbors[j] in initial_locations &&
          !(neighbors[j] in initial_locations[locationKeys[i]].neighbors)
        ) {
          initial_locations[locationKeys[i]].neighbors.add(neighbors[j]);
          initial_locations[neighbors[j]].neighbors.add(locationKeys[i]);
        }
      }
    }
  }

  // set up region unlock order for reachable locations and biomes
  const final_locations: Record<string, Location> = {};
  const final_biomes: Record<string, Biome> = {};

  final_locations[starting_location] = initial_locations[starting_location];
  delete initial_locations[starting_location];
  final_locations[starting_location].unlock_lvl = 0;
  final_biomes[final_locations[starting_location].biome] =
    initial_biomes[final_locations[starting_location].biome];
  final_biomes[final_locations[starting_location].biome].unlock_lvl = 0;

  let lvl = 1;
  outerLoop: while (true) {
    for (const location in final_locations) {
      for (const neighbor of final_locations[location].neighbors) {
        if (
          !(neighbor in final_locations) &&
          initial_locations[neighbor].biome in final_biomes
        ) {
          //console.log(neighbor);
          final_locations[neighbor] = initial_locations[neighbor];
          final_locations[neighbor].unlock_lvl = lvl;
          delete initial_locations[neighbor];
          continue outerLoop;
        }
      }
    }
    for (const location in final_locations) {
      for (const neighbor of final_locations[location].neighbors) {
        if (!(neighbor in final_locations)) {
          const new_biome: string = initial_locations[neighbor].biome;
          //console.log('*', new_biome);
          if (Object.keys(final_locations).length > 1) {
            ++lvl;
          }
          final_biomes[new_biome] = initial_biomes[new_biome];
          final_biomes[new_biome].unlock_lvl = lvl;
          delete initial_biomes[new_biome];
          continue outerLoop;
        }
      }
    }
    break;
  }

  // error checks and return
  if (Object.keys(final_locations).length < 7) {
    throw new Error("Too few locations");
  }
  if (Object.keys(final_biomes).length < 3) {
    throw new Error("Too few biomes");
  }
  if (lvl < 2) {
    throw new Error("Too few levels");
  }
  if (!(final_location in final_locations)) {
    throw new Error("Crypt not found");
  }

  return { locations: final_locations, biomes: final_biomes };
}

export function get_travel_locations(
  location_name: string,
  lvl: number,
  state_locations: Record<string, Location>,
) {
  const unlocked_locations: Set<string> = new Set();
  const locked_locations: Set<string> = new Set();

  for (const location of state_locations[location_name].neighbors) {
    if (state_locations[location].unlock_lvl <= lvl) {
      unlocked_locations.add(location);
    } else {
      locked_locations.add(location);
    }
  }

  return [unlocked_locations, locked_locations];
}

// const state_locations: Record<string, Location> = create_map_graph_and_biomes(neighbours, biomes, "Thornwood").locations;
