import { type MastraLanguageModel, Agent } from "@mastra/core/agent";

export type ArtistAgentParams = {
  model: MastraLanguageModel;
  style: string;
}

export function createArtistAgent(params: ArtistAgentParams) {
	const { model, style } = params;

	const agent = new Agent({
		name: "ArtistAgent",
		instructions: `You are an artist who can generate images based on the input. Your style is ${style}`,
		model,
	});

	return agent;
}