import { google } from "@ai-sdk/google";
import { groq } from "@ai-sdk/groq";
import { openai } from "@ai-sdk/openai";
import { Agent } from "@mastra/core";
import type { MastraLanguageModel } from "@mastra/core/agent";
import type { RuntimeContext } from "@mastra/core/runtime-context";
import { ollama } from "ollama-ai-provider";
import { world_agent } from "../../constants/world-agent";

export type WorldAgentRuntimeContext = {
	type: "create-world" | "analyze-world-map" | "analyze-world-location";
};

export function createWorldAgent(model: MastraLanguageModel) {
	const agent = new Agent({
		name: world_agent.name,
		description: "You are a world agent that can create and analyze worlds",
		instructions: ({ runtimeContext }) => {
			const type = (runtimeContext as RuntimeContext<WorldAgentRuntimeContext>).get("type");
			if (type === "create-world") {
				return world_agent.crate_world_system_prompt;
			}
			if (type === "analyze-world-location") {
				return world_agent.world_location_system_prompt;
			}
			if (type === "analyze-world-map") {
				return world_agent.world_map_system_prompt;
			}
			throw new Error("Invalid runtime context");
		},
		model: ({ runtimeContext }) => {
			const type = (runtimeContext as RuntimeContext<WorldAgentRuntimeContext>).get("type");
			if (type === "create-world") {
				return model;
			}
			if (type === "analyze-world-location") {
				return groq("meta-llama/llama-4-scout-17b-16e-instruct");
			}
			if (type === "analyze-world-map") {
				return openai("gpt-5");
			}
			throw new Error("Invalid runtime context");
		},
	});

	return agent;
}

export type WorldAgent = ReturnType<typeof createWorldAgent>;
