import { Agent } from "@mastra/core";
import { create_memory } from "./memory";
import type { INpcAgentParams } from "./npc";

export function createMerchantAgent(params: INpcAgentParams) {
	const { character, model } = params;

	const memory = create_memory({
		type: "sqlite",
		template: `
			# Merchant Status
			- Inventory: {{inventory}}
			- Currency: {{currency}}
			- Supplier Relations: {{suppliers}}
			- Customer Relations: {{customers}}
		`,
	});

	const agent = new Agent({
		name: character.name,
		instructions: character.system_prompt,
		model,
		memory,
		tools: {},
	});

	return agent;
}

export type MerchantAgent = ReturnType<typeof createMerchantAgent>;
