import { Agent, type MastraLanguageModel } from "@mastra/core/agent";
import type { Character } from "@/orchestrator";
import { create_memory } from "../agents/memory";

export type PlayerParams = {
	id: string;
	name: string;
	character: Character;
	model: MastraLanguageModel;
};

export function createPlayerAgent(params: PlayerParams) {
	const { name, character, model } = params;

	const agent = new Agent({
		name,
		instructions: character.system_prompt,
		model,
		memory: create_memory({
			type: "sqlite",
			enabled_working_memory: true,
			lastMessages: 10,
			generateTitle: true,
			topK: 10,
			messageRange: 10,
			template: "{{title}}",
		}),
		tools: {},
	});

	return agent;
}

export type PlayerAgent = ReturnType<typeof createPlayerAgent>;
