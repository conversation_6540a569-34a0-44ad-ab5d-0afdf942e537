import { Agent, type MastraLanguageModel } from "@mastra/core/agent";
import type { Character } from "../../orchestrator/types";
import { create_memory } from "./memory";

export interface INpcAgentParams {
	playerId: string;
	worldId: string;
	character: Character;
	model: MastraLanguageModel;
}

export function createNpcAgent(params: INpcAgentParams) {
	const memory = create_memory({
		type: "sqlite",
		enabled_working_memory: true,
		generateTitle: true,
		lastMessages: 5,
		topK: 3,
		messageRange: 10,
		template: `
      Always include every attribute of the user in your response
      {
        "user": "{{user}}",
        "appearance": "{{appearance}}",
        "background": "{{background}}",
        "personality": "{{personality}}",
        "interests": "{{interests}}",
        "goals": "{{goals}}",
        "relationships": "{{relationships}}",
      }
    `,
	});

	const agent = new Agent({
		name: params.character.name,
		instructions: params.character.system_prompt,
		model: params.model,
		memory,
	});

	return agent;
}

export type NpcAgent = ReturnType<typeof createNpcAgent>;
