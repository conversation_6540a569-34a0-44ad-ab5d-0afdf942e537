import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, MastraVector, MemoryConfig } from "@mastra/core";
import { LibSQLStore, LibSQLVector } from "@mastra/libsql";
import { Memory } from "@mastra/memory";
import { PgVector, PostgresStore } from "@mastra/pg";
import { z } from "zod";
import { get_memory_embedder_model, parsePostgresConnectionString } from "@/utils/helpers";

export const CreateMemoryArgsSchema = z.object({
	type: z.enum(["postgres", "sqlite"]),
	enabled_working_memory: z.boolean().default(true).optional(),
	template: z.string().optional(),
	lastMessages: z.number().default(10).optional(),
	generateTitle: z.boolean().default(true).optional(),
	topK: z.number().default(3).optional(),
	messageRange: z.number().default(4).optional(),
});

export type CreateMemoryArgs = z.infer<typeof CreateMemoryArgsSchema>;

export function create_memory(args: CreateMemoryArgs) {
	const { type, enabled_working_memory, template, lastMessages, generateTitle, topK, messageRange } = CreateMemoryArgsSchema.parse(args);

	const options: MemoryConfig = {
		workingMemory: {
			enabled: enabled_working_memory,
			template,
		},
		lastMessages,
		semanticRecall: {
			topK,
			messageRange,
		},
		threads: {
			generateTitle,
		},
	};

	let storage: MastraStorage;
	let vector: MastraVector;

	if (type === "postgres") {
		const connectionString = process.env.POSTGRES_CONNECTION_STRING;

		if (!connectionString) {
			throw new Error("POSTGRES_CONNECTION_STRING is not set");
		}

		const { host, database, user, password, port } = parsePostgresConnectionString(connectionString);
		storage = new PostgresStore({
			host,
			port,
			user,
			database,
			password,
			ssl: true,
		});

		vector = new PgVector({ connectionString });
	} else {
		const sqliteDbPath = process.env.SQLITE_DB_PATH;

		if (!sqliteDbPath) {
			throw new Error("SQLITE_DB_PATH is not set");
		}

		storage = new LibSQLStore({ url: sqliteDbPath });
		vector = new LibSQLVector({ connectionUrl: sqliteDbPath });
	}

	const memory = new Memory({
		storage,
		embedder: get_memory_embedder_model(),
		vector,
		options,
	});

	return memory;
}
