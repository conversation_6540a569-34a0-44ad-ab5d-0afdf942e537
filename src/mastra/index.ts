import { groq } from "@ai-sdk/groq";
import { openai } from "@ai-sdk/openai";
import { Ma<PERSON> } from "@mastra/core";
import { createOpenRouter } from "@openrouter/ai-sdk-provider";
import { createWorldAgent } from "./agents/world";
import { create_world_workflow } from "./workflows/world-flow";

export function create_mastra_instance() {
	const mastra = new Mastra({
		agents: {
			"world-agent": createWorldAgent(groq("moonshotai/kimi-k2-instruct")),
		},
		workflows: {
			create_world_workflow: create_world_workflow,
		},
		server: {
			build: {
				swaggerUI: true,
			},
		},
	});

	return mastra;
}
