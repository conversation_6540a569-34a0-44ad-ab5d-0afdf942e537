import { Surreal } from "surrealdb";

// import { drizzle } from "drizzle-orm/bun-sqlite";
// export const db = drizzle(process.env.DB_FILE_NAME!);

const url = process.env.SURREAL_DB_URL!;
if (!url) {
	throw new Error("SURREAL_DB_URL is not set");
}

const namespace = process.env.SURREAL_DB_NAMESPACE!;

if (!namespace) {
	throw new Error("SURREAL_DB_NAMESPACE is not set");
}

const database = process.env.SURREAL_DB_DATABASE!;

if (!database) {
	throw new Error("SURREAL_DB_DATABASE is not set");
}

const username = process.env.SURREAL_DB_USER!;
if (!username) {
	throw new Error("SURREAL_DB_USER is not set");
}

const password = process.env.SURREAL_DB_PASS!;
if (!password) {
	throw new Error("SURREAL_DB_PASSWORD is not set");
}

export const getSurrealDb = async () => {
	const db = new Surreal();
	await db.connect(url, {
		namespace,
		database,
		auth: { username, password },
	});
	return db;
};

export const db = await getSurrealDb();
