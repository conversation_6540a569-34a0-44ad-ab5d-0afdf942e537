import { RecordId } from "surrealdb";
import { z } from "zod";
import { BiomeSchema, LocationSchema, worldConfigSchema } from "@/orchestrator";
import { getSurrealDb } from ".";
import { user_schema } from "./user";

export const recordIdSchema = z.instanceof(RecordId);
export const TABLE_NAME = "world";

export const world_schema = z.object({
	id: recordIdSchema,
	version: z.number().default(0),
	config: worldConfigSchema.extend({
		starting_location: z.string(),
		final_location: z.string(),
		biomes: z.record(z.string(), BiomeSchema),
		locations: z.record(z.string(), LocationSchema),
	}),
	coverUrl: z.string(),
	creator: user_schema,
	// will add more fields later
});

export type World = z.infer<typeof world_schema>;

export async function save_world(world: World) {
	const locations_new = {};

	for (const [location, location_data] of Object.entries(world.config.locations)) {
		locations_new[location] = {
			...location_data,
			neighbors: Array.from(location_data.neighbors),
		};
	}

	//save to db
	const db = await getSurrealDb();
	await db.insert(TABLE_NAME, {
		...world,
		config: {
			...world.config,
			locations: locations_new,
		},
	});
}

export async function update_world(world: World) {
	console.log(`${"\x1b[32m"}updating world...${"\x1b[0m"}`);
	const locations_new = {};

	for (const [location, location_data] of Object.entries(world.config.locations)) {
		locations_new[location] = {
			...location_data,
			neighbors: Array.from(location_data.neighbors),
		};
	}

	//save to db
	const db = await getSurrealDb();
	await db.merge(world.id, {
		...world,
		config: {
			...world.config,
			locations: locations_new,
		},
	});
	console.log(`${"\x1b[32m"}updated world${"\x1b[0m"}`);
}

export async function load_world(id: string) {
	const db = await getSurrealDb();
	const world = await db.select<World>(new RecordId(TABLE_NAME, id));
	const locations_new = {};

	for (const [location, location_data] of Object.entries(world.config.locations)) {
		locations_new[location] = {
			...location_data,
			neighbors: new Set(location_data.neighbors),
		};
	}

	return {
		...world,
		config: {
			...world.config,
			locations: locations_new,
		},
	};
}

export async function fetch_worlds() {
	const db = await getSurrealDb();
	const worlds = await db.select<World>(TABLE_NAME);
	return worlds;
}

// todo: we need to store the world state

// example of world state:
// {
//   "world_id": "world-1",
//   "world_name": "The World of Eldoria",
//   "world_description": "A world filled with magic and adventure, where players can explore the world and interact with the characters.",
//   "world_state": {
//     "time": "10:00 AM",
//     "weather": "Sunny",
//     "weather_description": "The sun is shining brightly, and the birds are chirping.",
//   }
// }

// logs of events, player actions, etc:
// {
//   "world_id": "world-1",
//   "player_id": "player-1",
//   "logs": [
//     "The player has entered the forest of Eldoria.",
//     "The player has found a sword in the forest.",
