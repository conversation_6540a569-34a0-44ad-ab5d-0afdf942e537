import { RecordId } from "surrealdb";
import { z } from "zod";
import { BiomeSchema, LocationSchema, PlayerSchema, worldConfigSchema } from "@/orchestrator";
import { getSurrealDb } from ".";
import { user_schema } from "./user";

export const recordIdSchema = z.instanceof(RecordId);
export const TABLE_NAME = "playthrough";

export const playthrough_schema = z.object({
	id: recordIdSchema,
    version: z.number().default(0),
    player: PlayerSchema,
	world: recordIdSchema,
    creator: user_schema,
	// will add more fields later
});

export type Playthrough = z.infer<typeof playthrough_schema>;

export async function save_playthrough(playthrough: Playthrough) {
    const db = await getSurrealDb();
    
    await db.insert(TABLE_NAME, playthrough);
}

export async function update_playthrough(playthrough: Playthrough) {
    console.log(`${"\x1b[32m"}updating playthrough...${"\x1b[0m"}`);
    const db = await getSurrealDb();
    
    await db.merge(playthrough.id, playthrough);
    console.log(`${"\x1b[32m"}updated playthrough${"\x1b[0m"}`);
}

export async function load_playthrough(id: string) {
    const db = await getSurrealDb();
    const playthrough = await db.select<Playthrough>(new RecordId(TABLE_NAME, id));
    
    return playthrough;
}

export async function fetch_playthroughs() {
    const db = await getSurrealDb();
    const playthroughs = await db.select<Playthrough>(TABLE_NAME);
    
    return playthroughs;
}